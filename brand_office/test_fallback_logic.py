#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Fallback Logic: Kiểm tra logic fallback với check_point_in_rings
"""

import json
import logging
import time
import pandas as pd
import geopandas as gpd
import mysql.connector
from mysql.connector import Error
from shapely import make_valid
from shapely.geometry import Point, shape, Polygon
import warnings
import asyncio
import os
warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('exports/test_fallback.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FallbackTester:
    def __init__(self):
        self.connection = None
        
    def get_database_connection(self):
        """Tạo kết nối database"""
        try:
            connection = mysql.connector.connect(
                host='127.0.0.1',
                port=3306,
                database='urbox',
                user='root',
                password='root',
                charset='utf8mb4'
            )
            
            if connection.is_connected():
                logger.info("✅ Kết nối database thành công!")
                return connection
                
        except Error as e:
            logger.error(f"❌ Lỗi kết nối database: {e}")
            return None
    
    def check_point_in_rings(self, point: Point, rings: list):
        """
        Kiểm tra xem một điểm có nằm trong bất kỳ Polygon nào được tạo từ các rings không.
        """
        logger.info(f"🔍 Checking point {point} với {len(rings)} rings")

        # Danh sách để lưu các Polygon
        polygons = []
        current_exterior = None
        current_interiors = []

        # Phân tích rings để tạo Polygon
        for ring_idx, ring in enumerate(rings):
            try:
                # Đảm bảo ring khép kín
                if ring and ring[0] != ring[-1]:
                    ring.append(ring[0])

                # Tạo Polygon tạm để xác định hướng (exterior hay interior)
                temp_poly = Polygon(ring)
                if not temp_poly.is_valid:
                    temp_poly = make_valid(temp_poly)
                    if not temp_poly.is_valid:
                        continue

                # Nếu đây là exterior ring mới
                if current_exterior is None:
                    current_exterior = ring
                    current_interiors = []
                else:
                    # Kiểm tra xem ring có phải là interior ring không
                    exterior_poly = Polygon(current_exterior)
                    if exterior_poly.is_valid and temp_poly.is_valid and exterior_poly.contains(temp_poly):
                        current_interiors.append(ring)
                    else:
                        # Kết thúc Polygon hiện tại và bắt đầu Polygon mới
                        if current_exterior:
                            poly = Polygon(current_exterior, current_interiors)
                            if poly.is_valid:
                                polygons.append(poly)
                            else:
                                poly = make_valid(poly)
                                if poly.is_valid:
                                    polygons.append(poly)
                        current_exterior = ring
                        current_interiors = []

            except Exception as e:
                logger.warning(f"⚠️ Lỗi xử lý ring {ring_idx}: {e}")
                continue

        # Tạo Polygon cuối cùng (nếu còn)
        if current_exterior:
            try:
                poly = Polygon(current_exterior, current_interiors)
                if poly.is_valid:
                    polygons.append(poly)
                else:
                    poly = make_valid(poly)
                    if poly.is_valid:
                        polygons.append(poly)
            except Exception as e:
                logger.warning(f"⚠️ Lỗi tạo Polygon cuối: {e}")

        logger.info(f"📊 Tạo được {len(polygons)} polygons từ {len(rings)} rings")

        # Kiểm tra điểm trong từng Polygon
        for poly_idx, poly in enumerate(polygons):
            try:
                if not poly.is_valid:
                    continue

                # Kiểm tra điểm trong Polygon
                if poly.contains(point):
                    logger.info(f"✅ Điểm nằm trong Polygon {poly_idx} (contains)")
                    return {"polygon_index": poly_idx}, 'contains'
                elif poly.intersects(point):
                    logger.info(f"✅ Điểm giao với Polygon {poly_idx} (intersects)")
                    return {"polygon_index": poly_idx}, 'intersects'

                # Kiểm tra khoảng cách gần nhất
                distance = poly.distance(point)
                if distance < 0.0001:  # Ngưỡng ~11 mét
                    logger.info(f"✅ Điểm gần Polygon {poly_idx} (khoảng cách {distance:.6f})")
                    return {"polygon_index": poly_idx, "distance": distance}, 'near'

            except Exception as e:
                logger.warning(f"⚠️ Lỗi kiểm tra Polygon {poly_idx}: {e}")
                continue

        logger.info("❌ Không tìm thấy Polygon nào chứa điểm")
        return None, 'no_match'
    
    def test_fallback_logic(self):
        """Test logic fallback với check_point_in_rings"""
        logger.info("=" * 80)
        logger.info("🚀 TEST FALLBACK LOGIC WITH CHECK_POINT_IN_RINGS")
        logger.info("=" * 80)
        
        self.connection = self.get_database_connection()
        if not self.connection:
            return
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            # Lấy geometry từ database (rings format)
            query = """
            SELECT id, ward_title, province_title, KINH_DO, VI_DO, geometry
            FROM geo_ward 
            WHERE id = 726 AND geometry IS NOT NULL
            """
            cursor.execute(query)
            result = cursor.fetchone()
            cursor.close()
            
            if not result:
                logger.error("❌ Không tìm thấy bản ghi test")
                return
            
            logger.info(f"📍 Test với: {result['ward_title']}, {result['province_title']}")
            logger.info(f"📍 Geometry center: ({result['VI_DO']}, {result['KINH_DO']})")
            
            # Parse geometry để lấy rings
            geometry_data = json.loads(result['geometry'])
            logger.info(f"🔍 Geometry data keys: {list(geometry_data.keys())}")

            if 'geometry' in geometry_data:
                geom_obj = geometry_data['geometry']
            else:
                geom_obj = geometry_data

            logger.info(f"🔍 Geometry object keys: {list(geom_obj.keys())}")
            logger.info(f"🔍 Geometry type: {geom_obj.get('type', 'Unknown')}")

            if 'rings' not in geom_obj:
                logger.error("❌ Geometry không có rings format")
                logger.info(f"🔍 Available keys: {list(geom_obj.keys())}")
                return
            
            rings = geom_obj['rings']
            
            # Flatten nested rings structure
            flat_rings = []
            for ring_group in rings:
                if isinstance(ring_group[0][0], list):
                    # Nested structure: [[[lon,lat], [lon,lat], ...]]
                    for ring in ring_group:
                        flat_rings.append(ring)
                else:
                    # Flat structure: [[lon,lat], [lon,lat], ...]
                    flat_rings.append(ring_group)
            
            logger.info(f"🔍 Flattened to {len(flat_rings)} rings")
            
            # Test points
            test_points = [
                (float(result['VI_DO']), float(result['KINH_DO']), "Geometry Center"),
                (20.95026, 107.08415, "Custom Test Point"),
                (20.957, 107.083, "Near Center"),
                (21.0, 106.0, "Far Point (should be False)")
            ]
            
            for lat, lng, description in test_points:
                logger.info(f"\n" + "=" * 50)
                logger.info(f"🎯 Testing: {description} ({lat}, {lng})")
                logger.info("=" * 50)
                
                point = Point(lng, lat)
                
                # Test với check_point_in_rings
                start_time = time.time()
                result_dict, match_type = self.check_point_in_rings(point, flat_rings)
                processing_time = time.time() - start_time
                
                logger.info(f"📊 Result: {match_type}")
                logger.info(f"⏱️ Processing time: {processing_time:.6f}s")
                
                if match_type in ['contains', 'intersects', 'near']:
                    logger.info(f"🎉 Point {description} found in geometry!")
                    if result_dict:
                        logger.info(f"📊 Details: {result_dict}")
                else:
                    logger.info(f"❌ Point {description} not found in geometry")
            
            logger.info("\n" + "=" * 50)
            logger.info("📊 FALLBACK TEST SUMMARY")
            logger.info("=" * 50)
            logger.info("✅ check_point_in_rings function hoạt động")
            logger.info("✅ Có thể xử lý rings format từ database")
            logger.info("✅ Phù hợp làm fallback cho spatial index")
            
        except Exception as e:
            logger.error(f"❌ Lỗi trong test: {e}")
        finally:
            if self.connection:
                self.connection.close()

def main():
    """Hàm main"""
    tester = FallbackTester()
    tester.test_fallback_logic()

if __name__ == "__main__":
    main()
