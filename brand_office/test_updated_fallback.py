#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Updated Fallback Logic: Kiểm tra logic fallback đã được cập nhật
"""

import json
import logging
import time
import pandas as pd
import geopandas as gpd
import mysql.connector
from mysql.connector import <PERSON>rror
from shapely import make_valid
from shapely.geometry import Point, shape, Polygon
import warnings
import asyncio
import os
warnings.filterwarnings('ignore')

# Import class từ file chính
import sys
sys.path.append('.')
from update_brand_office_address import BrandOfficeAddressUpdater

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('exports/test_updated_fallback.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def test_fallback_with_empty_spatial_index():
    """Test fallback logic khi spatial index không tìm thấy candidates"""
    logger.info("=" * 80)
    logger.info("🚀 TEST UPDATED FALLBACK LOGIC")
    logger.info("=" * 80)
    
    # Tạo instance của BrandOfficeAddressUpdater
    updater = BrandOfficeAddressUpdater()
    
    # Kết nối database
    updater.connection = updater.get_database_connection()
    if not updater.connection:
        logger.error("❌ Không thể kết nối database")
        return
    
    try:
        # Tạo một GeoDataFrame rỗng để simulate trường hợp spatial index không tìm thấy
        empty_gdf = gpd.GeoDataFrame()
        
        # Test points - sử dụng tọa độ đã biết nằm trong geometry
        test_points = [
            (20.957205063246104, 107.0825959978276, "Geometry Center"),
            (20.95026, 107.08415, "Custom Test Point"),
            (21.0, 106.0, "Far Point (should not be found)")
        ]
        
        for lat, lng, description in test_points:
            logger.info(f"\n" + "=" * 50)
            logger.info(f"🎯 Testing: {description} ({lat}, {lng})")
            logger.info("=" * 50)
            
            # Gọi find_ward_by_lat_lng với empty GeoDataFrame
            # Điều này sẽ trigger fallback logic
            start_time = time.time()
            ward_dict, match_type = updater.find_ward_by_lat_lng(lat, lng, empty_gdf)
            processing_time = time.time() - start_time
            
            logger.info(f"📊 Result: {match_type}")
            logger.info(f"⏱️ Processing time: {processing_time:.6f}s")
            
            if ward_dict:
                logger.info(f"🎉 Found ward: {ward_dict.get('ward_title', 'Unknown')}")
                logger.info(f"📍 Province: {ward_dict.get('province_title', 'Unknown')}")
                logger.info(f"🔢 Ward ID: {ward_dict.get('id', 'Unknown')}")
            else:
                logger.info(f"❌ No ward found for {description}")
        
        logger.info("\n" + "=" * 50)
        logger.info("📊 FALLBACK TEST SUMMARY")
        logger.info("=" * 50)
        logger.info("✅ Fallback logic hoạt động với empty spatial index")
        logger.info("✅ Có thể xử lý coordinates format từ database")
        logger.info("✅ Logic fallback tự động được trigger")
        
    except Exception as e:
        logger.error(f"❌ Lỗi trong test: {e}")
    finally:
        if updater.connection:
            updater.connection.close()

def test_normal_vs_fallback():
    """So sánh kết quả giữa normal spatial index và fallback"""
    logger.info("\n" + "=" * 80)
    logger.info("🔄 COMPARISON: NORMAL VS FALLBACK")
    logger.info("=" * 80)
    
    updater = BrandOfficeAddressUpdater()
    updater.connection = updater.get_database_connection()
    
    if not updater.connection:
        return
    
    try:
        # Lấy geo_ward data bình thường
        geo_ward_gdf = updater.get_geo_ward_data()
        
        if geo_ward_gdf.empty:
            logger.error("❌ Không có geo_ward data")
            return
        
        # Test point
        test_lat, test_lng = 20.95026, 107.08415
        
        logger.info(f"🎯 Testing point: ({test_lat}, {test_lng})")
        
        # Test với spatial index bình thường
        logger.info("\n📊 NORMAL SPATIAL INDEX:")
        start_time = time.time()
        normal_result, normal_type = updater.find_ward_by_lat_lng(test_lat, test_lng, geo_ward_gdf)
        normal_time = time.time() - start_time
        
        logger.info(f"  Result: {normal_type}")
        logger.info(f"  Time: {normal_time:.6f}s")
        if normal_result:
            logger.info(f"  Ward: {normal_result.get('ward_title', 'Unknown')}")
        
        # Test với empty GeoDataFrame (trigger fallback)
        logger.info("\n📊 FALLBACK LOGIC:")
        empty_gdf = gpd.GeoDataFrame()
        start_time = time.time()
        fallback_result, fallback_type = updater.find_ward_by_lat_lng(test_lat, test_lng, empty_gdf)
        fallback_time = time.time() - start_time
        
        logger.info(f"  Result: {fallback_type}")
        logger.info(f"  Time: {fallback_time:.6f}s")
        if fallback_result:
            logger.info(f"  Ward: {fallback_result.get('ward_title', 'Unknown')}")
        
        # So sánh kết quả
        logger.info("\n📊 COMPARISON:")
        if normal_result and fallback_result:
            if normal_result.get('ward_title') == fallback_result.get('ward_title'):
                logger.info("✅ Kết quả nhất quán giữa normal và fallback")
            else:
                logger.warning("⚠️ Kết quả khác nhau giữa normal và fallback")
                logger.info(f"  Normal: {normal_result.get('ward_title')}")
                logger.info(f"  Fallback: {fallback_result.get('ward_title')}")
        
        if normal_time < fallback_time:
            logger.info(f"🚀 Normal faster by {(fallback_time/normal_time):.2f}x")
        else:
            logger.info(f"🚀 Fallback faster by {(normal_time/fallback_time):.2f}x")
        
    except Exception as e:
        logger.error(f"❌ Lỗi trong comparison test: {e}")
    finally:
        if updater.connection:
            updater.connection.close()

def main():
    """Hàm main"""
    test_fallback_with_empty_spatial_index()
    test_normal_vs_fallback()

if __name__ == "__main__":
    main()
