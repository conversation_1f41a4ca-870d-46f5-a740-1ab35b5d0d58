#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Geometry Conversion: Coordinates ↔ Rings với Brand Office Integration
Demo chuyển đổi ngượ<PERSON> từ coordinates sang rings và test với brand_office data
"""

import json
import mysql.connector
from mysql.connector import Error
from shapely.geometry import Point, shape, Polygon, MultiPolygon
import logging
import time
from typing import Dict, Any, Optional, Tuple

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('exports/geometry_conversion_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class GeometryConversionTester:
    """Test chuyển đổi geometry formats và tích hợp với brand_office"""
    
    def __init__(self):
        self.connection = None
        self.conversion_stats = {
            'coordinates_to_rings': 0,
            'rings_to_coordinates': 0,
            'conversion_errors': 0,
            'point_in_polygon_tests': 0,
            'brand_office_matches': 0
        }
        
    def get_database_connection(self):
        """Tạo kết nối database MySQL"""
        try:
            connection = mysql.connector.connect(
                host='127.0.0.1',
                port=3306,
                database='urbox',
                user='root',
                password='root',
                charset='utf8mb4'
            )
            
            if connection.is_connected():
                logger.info("✅ Kết nối database thành công!")
                return connection
                
        except Error as e:
            logger.error(f"❌ Lỗi kết nối database: {e}")
            return None
    
    def convert_coordinates_to_rings(self, geom_obj: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Chuyển đổi từ coordinates format (GeoJSON) sang rings format (Esri)"""
        try:
            if 'coordinates' in geom_obj:
                rings_geom = geom_obj.copy()
                coordinates = geom_obj['coordinates']
                
                # Xử lý theo type của geometry
                if geom_obj.get('type') == 'MultiPolygon':
                    # MultiPolygon: coordinates = [[[[lon,lat], ...]]]
                    rings = []
                    for polygon in coordinates:
                        for ring in polygon:
                            rings.append(ring)
                    rings_geom['rings'] = rings
                    
                elif geom_obj.get('type') == 'Polygon':
                    # Polygon: coordinates = [[[lon,lat], ...]]
                    rings_geom['rings'] = coordinates
                    
                # Xóa coordinates và cập nhật type
                rings_geom.pop('coordinates', None)
                rings_geom['type'] = 'esriGeometryPolygon'
                
                self.conversion_stats['coordinates_to_rings'] += 1
                logger.debug(f"✅ Chuyển đổi coordinates->rings: {len(rings_geom.get('rings', []))} rings")
                return rings_geom
                
            elif 'rings' in geom_obj:
                logger.debug("ℹ️ Geometry đã ở định dạng rings")
                return geom_obj
                
            else:
                logger.warning("⚠️ Không tìm thấy coordinates hoặc rings trong geometry")
                self.conversion_stats['conversion_errors'] += 1
                return None
                
        except Exception as e:
            logger.error(f"❌ Lỗi chuyển đổi coordinates->rings: {e}")
            self.conversion_stats['conversion_errors'] += 1
            return None
    
    def convert_rings_to_coordinates(self, geom_obj: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Chuyển đổi từ rings format (Esri) sang coordinates format (GeoJSON)"""
        try:
            if 'rings' in geom_obj:
                coords_geom = geom_obj.copy()
                rings = geom_obj['rings']
                
                # Flatten nested rings structure nếu cần
                flat_rings = []
                for ring_group in rings:
                    if isinstance(ring_group[0][0], list):
                        # Nested structure: [[[lon,lat], [lon,lat], ...]]
                        for ring in ring_group:
                            flat_rings.append(ring)
                    else:
                        # Flat structure: [[lon,lat], [lon,lat], ...]
                        flat_rings.append(ring_group)
                
                # Tạo coordinates structure cho GeoJSON
                if len(flat_rings) == 1:
                    coords_geom['coordinates'] = [flat_rings[0]]
                    coords_geom['type'] = 'Polygon'
                else:
                    # Multi-polygon
                    polygons = []
                    for ring in flat_rings:
                        polygons.append([ring])
                    coords_geom['coordinates'] = polygons
                    coords_geom['type'] = 'MultiPolygon'
                
                # Xóa rings
                coords_geom.pop('rings', None)
                
                self.conversion_stats['rings_to_coordinates'] += 1
                logger.debug(f"✅ Chuyển đổi rings->coordinates: {coords_geom['type']}")
                return coords_geom
                
            elif 'coordinates' in geom_obj:
                logger.debug("ℹ️ Geometry đã ở định dạng coordinates")
                return geom_obj
                
            else:
                logger.warning("⚠️ Không tìm thấy rings hoặc coordinates trong geometry")
                self.conversion_stats['conversion_errors'] += 1
                return None
                
        except Exception as e:
            logger.error(f"❌ Lỗi chuyển đổi rings->coordinates: {e}")
            self.conversion_stats['conversion_errors'] += 1
            return None
    
    def create_shapely_from_rings(self, geom_obj: Dict[str, Any]) -> Optional[Any]:
        """Tạo Shapely geometry từ rings format"""
        try:
            rings = geom_obj.get('rings', [])
            if not rings:
                return None
            
            # Flatten nested rings structure
            flat_rings = []
            for ring_group in rings:
                if isinstance(ring_group[0][0], list):
                    for ring in ring_group:
                        flat_rings.append(ring)
                else:
                    flat_rings.append(ring_group)
            
            # Tạo polygons từ rings
            polygons = []
            for ring in flat_rings:
                try:
                    poly = Polygon(ring)
                    if poly.is_valid:
                        polygons.append(poly)
                    else:
                        # Try to fix invalid polygon
                        fixed_poly = poly.buffer(0)
                        if fixed_poly.is_valid:
                            polygons.append(fixed_poly)
                except Exception as e:
                    logger.warning(f"⚠️ Skipping invalid ring: {e}")
                    continue
            
            if len(polygons) == 0:
                return None
            elif len(polygons) == 1:
                return polygons[0]
            else:
                return MultiPolygon(polygons)
                
        except Exception as e:
            logger.error(f"❌ Lỗi tạo Shapely từ rings: {e}")
            return None
    
    def test_point_in_polygon_both_formats(self, geometry_data: Dict[str, Any], test_point: Tuple[float, float]) -> Dict[str, Any]:
        """Test point-in-polygon với cả rings và coordinates format"""
        try:
            lat, lng = test_point
            point = Point(lng, lat)
            
            # Lấy geometry object từ feature
            if 'geometry' in geometry_data:
                geom_obj = geometry_data['geometry']
            else:
                geom_obj = geometry_data
            
            results = {
                'test_point': test_point,
                'rings_result': None,
                'coordinates_result': None,
                'rings_time': 0,
                'coordinates_time': 0,
                'conversion_success': False,
                'original_format': None,
                'consistent_results': False
            }
            
            # Detect original format
            if 'rings' in geom_obj:
                results['original_format'] = 'rings'
                
                # Test với rings format
                start_time = time.time()
                rings_geometry = self.create_shapely_from_rings(geom_obj)
                if rings_geometry:
                    results['rings_result'] = rings_geometry.contains(point)
                results['rings_time'] = time.time() - start_time
                
                # Chuyển đổi sang coordinates và test
                coords_geom = self.convert_rings_to_coordinates(geom_obj)
                if coords_geom:
                    start_time = time.time()
                    coords_geometry = shape(coords_geom)
                    if coords_geometry:
                        results['coordinates_result'] = coords_geometry.contains(point)
                        results['conversion_success'] = True
                    results['coordinates_time'] = time.time() - start_time
            
            elif 'coordinates' in geom_obj:
                results['original_format'] = 'coordinates'
                
                # Test với coordinates format
                start_time = time.time()
                coords_geometry = shape(geom_obj)
                if coords_geometry:
                    results['coordinates_result'] = coords_geometry.contains(point)
                results['coordinates_time'] = time.time() - start_time
                
                # Chuyển đổi sang rings và test
                rings_geom = self.convert_coordinates_to_rings(geom_obj)
                if rings_geom:
                    start_time = time.time()
                    rings_geometry = self.create_shapely_from_rings(rings_geom)
                    if rings_geometry:
                        results['rings_result'] = rings_geometry.contains(point)
                        results['conversion_success'] = True
                    results['rings_time'] = time.time() - start_time
            
            # Check consistency
            if (results['rings_result'] is not None and 
                results['coordinates_result'] is not None):
                results['consistent_results'] = (results['rings_result'] == results['coordinates_result'])
            
            self.conversion_stats['point_in_polygon_tests'] += 1
            return results
                
        except Exception as e:
            logger.error(f"❌ Lỗi test point-in-polygon: {e}")
            return {'error': str(e)}
    
    def get_sample_data(self):
        """Lấy dữ liệu mẫu từ geo_ward và brand_office"""
        self.connection = self.get_database_connection()
        if not self.connection:
            return None, None
            
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            # Lấy geometry mẫu từ geo_ward
            geo_query = """
            SELECT id, ward_title, province_title, KINH_DO, VI_DO, geometry
            FROM geo_ward 
            WHERE geometry IS NOT NULL 
            LIMIT 3
            """
            cursor.execute(geo_query)
            geo_results = cursor.fetchall()
            
            # Lấy brand_office mẫu
            brand_query = """
            SELECT id, latitude, longitude, address_old, city_id
            FROM brand_office 
            WHERE latitude IS NOT NULL AND longitude IS NOT NULL
            AND latitude != 0 AND longitude != 0
            AND status = 2
            LIMIT 5
            """
            cursor.execute(brand_query)
            brand_results = cursor.fetchall()
            
            cursor.close()
            
            logger.info(f"📊 Lấy được {len(geo_results)} geo_ward và {len(brand_results)} brand_office")
            return geo_results, brand_results
            
        except Exception as e:
            logger.error(f"❌ Lỗi lấy dữ liệu mẫu: {e}")
            return None, None
        finally:
            if self.connection:
                self.connection.close()
    
    def run_conversion_test(self):
        """Chạy test chuyển đổi geometry format"""
        logger.info("=" * 80)
        logger.info("🚀 GEOMETRY CONVERSION TEST: COORDINATES ↔ RINGS")
        logger.info("=" * 80)
        
        # Lấy dữ liệu mẫu
        geo_data, brand_data = self.get_sample_data()
        if not geo_data or not brand_data:
            logger.error("❌ Không thể lấy dữ liệu test")
            return
        
        # Test với từng geometry và brand_office points
        for geo_record in geo_data:
            logger.info(f"\n📍 Testing geometry: {geo_record['ward_title']}, {geo_record['province_title']}")
            
            geometry_data = json.loads(geo_record['geometry'])
            
            # Test với một số brand_office points
            for brand_record in brand_data[:2]:  # Test với 2 brand_office đầu tiên
                test_point = (float(brand_record['latitude']), float(brand_record['longitude']))
                
                logger.info(f"🎯 Test point: Brand Office {brand_record['id']} at {test_point}")
                
                # Chạy test với cả hai format
                results = self.test_point_in_polygon_both_formats(geometry_data, test_point)
                
                if 'error' not in results:
                    logger.info(f"  📊 Original format: {results['original_format']}")
                    logger.info(f"  🎯 Rings result: {results['rings_result']} (time: {results['rings_time']:.6f}s)")
                    logger.info(f"  🎯 Coordinates result: {results['coordinates_result']} (time: {results['coordinates_time']:.6f}s)")
                    logger.info(f"  ✅ Conversion success: {results['conversion_success']}")
                    logger.info(f"  🔄 Results consistent: {results['consistent_results']}")
                    
                    if results['rings_result'] or results['coordinates_result']:
                        self.conversion_stats['brand_office_matches'] += 1
                        logger.info(f"  🎉 Brand Office {brand_record['id']} nằm trong ward!")
                else:
                    logger.error(f"  ❌ Test error: {results['error']}")
        
        # Log final statistics
        logger.info("\n" + "=" * 50)
        logger.info("📊 CONVERSION TEST STATISTICS")
        logger.info("=" * 50)
        for key, value in self.conversion_stats.items():
            logger.info(f"  {key}: {value}")

def main():
    """Hàm main để chạy test"""
    tester = GeometryConversionTester()
    tester.run_conversion_test()

if __name__ == "__main__":
    main()
