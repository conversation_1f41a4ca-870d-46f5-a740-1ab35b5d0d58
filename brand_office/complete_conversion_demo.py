#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Complete Demo: Chuyển đổi Coordinates ↔ Rings với Brand Office Integration
Minh họa đầy đủ chuyển đổi hai chiều và ứng dụng thực tế
"""

import json
import mysql.connector
from shapely.geometry import Point, shape, Polygon, MultiPolygon
import logging
import time

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GeometryConverter:
    """Class chuyển đổi geometry formats"""
    
    def __init__(self):
        self.stats = {
            'coordinates_to_rings': 0,
            'rings_to_coordinates': 0,
            'conversion_errors': 0,
            'point_tests': 0
        }
    
    def convert_coordinates_to_rings(self, geom_obj):
        """Chuyển đổi từ coordinates format (GeoJSON) sang rings format (Esri)"""
        try:
            if 'coordinates' in geom_obj:
                rings_geom = geom_obj.copy()
                coordinates = geom_obj['coordinates']
                
                if geom_obj.get('type') == 'MultiPolygon':
                    # MultiPolygon: coordinates = [[[[lon,lat], ...]]]
                    rings = []
                    for polygon in coordinates:
                        for ring in polygon:
                            rings.append(ring)
                    rings_geom['rings'] = rings
                    
                elif geom_obj.get('type') == 'Polygon':
                    # Polygon: coordinates = [[[lon,lat], ...]]
                    rings_geom['rings'] = coordinates
                    
                # Xóa coordinates và cập nhật type
                rings_geom.pop('coordinates', None)
                rings_geom['type'] = 'esriGeometryPolygon'
                
                self.stats['coordinates_to_rings'] += 1
                return rings_geom
                
            elif 'rings' in geom_obj:
                return geom_obj
            else:
                self.stats['conversion_errors'] += 1
                return None
                
        except Exception as e:
            logger.error(f"❌ Lỗi chuyển đổi coordinates->rings: {e}")
            self.stats['conversion_errors'] += 1
            return None
    
    def convert_rings_to_coordinates(self, geom_obj):
        """Chuyển đổi từ rings format (Esri) sang coordinates format (GeoJSON)"""
        try:
            if 'rings' in geom_obj:
                coords_geom = geom_obj.copy()
                rings = geom_obj['rings']
                
                # Flatten nested rings structure
                flat_rings = []
                for ring_group in rings:
                    if isinstance(ring_group[0][0], list):
                        for ring in ring_group:
                            flat_rings.append(ring)
                    else:
                        flat_rings.append(ring_group)
                
                # Tạo coordinates structure
                if len(flat_rings) == 1:
                    coords_geom['coordinates'] = [flat_rings[0]]
                    coords_geom['type'] = 'Polygon'
                else:
                    polygons = []
                    for ring in flat_rings:
                        polygons.append([ring])
                    coords_geom['coordinates'] = polygons
                    coords_geom['type'] = 'MultiPolygon'
                
                coords_geom.pop('rings', None)
                self.stats['rings_to_coordinates'] += 1
                return coords_geom
                
            elif 'coordinates' in geom_obj:
                return geom_obj
            else:
                self.stats['conversion_errors'] += 1
                return None
                
        except Exception as e:
            logger.error(f"❌ Lỗi chuyển đổi rings->coordinates: {e}")
            self.stats['conversion_errors'] += 1
            return None
    
    def create_shapely_from_rings(self, geom_obj):
        """Tạo Shapely geometry từ rings format"""
        try:
            rings = geom_obj.get('rings', [])
            if not rings:
                return None
            
            flat_rings = []
            for ring_group in rings:
                if isinstance(ring_group[0][0], list):
                    for ring in ring_group:
                        flat_rings.append(ring)
                else:
                    flat_rings.append(ring_group)
            
            polygons = []
            for ring in flat_rings:
                try:
                    poly = Polygon(ring)
                    if poly.is_valid:
                        polygons.append(poly)
                    else:
                        fixed_poly = poly.buffer(0)
                        if fixed_poly.is_valid:
                            polygons.append(fixed_poly)
                except:
                    continue
            
            if len(polygons) == 0:
                return None
            elif len(polygons) == 1:
                return polygons[0]
            else:
                return MultiPolygon(polygons)
                
        except Exception as e:
            logger.error(f"❌ Lỗi tạo Shapely từ rings: {e}")
            return None
    
    def test_point_in_polygon_both_formats(self, geom_obj, test_point):
        """Test point-in-polygon với cả hai format"""
        lat, lng = test_point
        point = Point(lng, lat)
        
        results = {
            'original_format': None,
            'rings_result': None,
            'coordinates_result': None,
            'rings_time': 0,
            'coordinates_time': 0,
            'conversion_success': False
        }
        
        # Detect original format
        if 'rings' in geom_obj:
            results['original_format'] = 'rings'
            
            # Test với rings
            start_time = time.time()
            rings_geometry = self.create_shapely_from_rings(geom_obj)
            if rings_geometry:
                results['rings_result'] = rings_geometry.contains(point)
            results['rings_time'] = time.time() - start_time
            
            # Convert và test với coordinates
            coords_geom = self.convert_rings_to_coordinates(geom_obj)
            if coords_geom:
                start_time = time.time()
                coords_geometry = shape(coords_geom)
                if coords_geometry:
                    results['coordinates_result'] = coords_geometry.contains(point)
                    results['conversion_success'] = True
                results['coordinates_time'] = time.time() - start_time
        
        elif 'coordinates' in geom_obj:
            results['original_format'] = 'coordinates'
            
            # Test với coordinates
            start_time = time.time()
            coords_geometry = shape(geom_obj)
            if coords_geometry:
                results['coordinates_result'] = coords_geometry.contains(point)
            results['coordinates_time'] = time.time() - start_time
            
            # Convert và test với rings
            rings_geom = self.convert_coordinates_to_rings(geom_obj)
            if rings_geom:
                start_time = time.time()
                rings_geometry = self.create_shapely_from_rings(rings_geom)
                if rings_geometry:
                    results['rings_result'] = rings_geometry.contains(point)
                    results['conversion_success'] = True
                results['rings_time'] = time.time() - start_time
        
        self.stats['point_tests'] += 1
        return results

def get_database_connection():
    """Tạo kết nối database"""
    try:
        connection = mysql.connector.connect(
            host='127.0.0.1',
            port=3306,
            database='urbox',
            user='root',
            password='root',
            charset='utf8mb4'
        )
        
        if connection.is_connected():
            logger.info("✅ Kết nối database thành công!")
            return connection
            
    except Exception as e:
        logger.error(f"❌ Lỗi kết nối database: {e}")
        return None

def demo_complete_conversion():
    """Demo hoàn chỉnh chuyển đổi và ứng dụng"""
    logger.info("=" * 80)
    logger.info("🚀 COMPLETE GEOMETRY CONVERSION DEMO")
    logger.info("=" * 80)
    
    converter = GeometryConverter()
    connection = get_database_connection()
    
    if not connection:
        return
    
    try:
        cursor = connection.cursor(dictionary=True)
        
        # Lấy geometry từ database (rings format)
        query = """
        SELECT id, ward_title, province_title, KINH_DO, VI_DO, geometry
        FROM geo_ward 
        WHERE id = 726 AND geometry IS NOT NULL
        """
        cursor.execute(query)
        geo_result = cursor.fetchone()
        
        # Lấy brand_office test points
        brand_query = """
        SELECT id, latitude, longitude, address_old
        FROM brand_office 
        WHERE id IN (485, 476) AND latitude IS NOT NULL
        """
        cursor.execute(brand_query)
        brand_results = cursor.fetchall()
        
        cursor.close()
        
        if not geo_result or not brand_results:
            logger.error("❌ Không có dữ liệu test")
            return
        
        logger.info(f"📍 Geometry: {geo_result['ward_title']}, {geo_result['province_title']}")
        logger.info(f"🏢 Testing với {len(brand_results)} brand offices")
        
        # Parse geometry
        geometry_data = json.loads(geo_result['geometry'])
        geom_obj = geometry_data['geometry']
        
        logger.info(f"\n🔍 Original format: {geom_obj.get('type', 'Unknown')}")
        logger.info(f"🔍 Has rings: {'rings' in geom_obj}")
        logger.info(f"🔍 Has coordinates: {'coordinates' in geom_obj}")
        
        # Demo 1: Chuyển đổi từ rings sang coordinates
        if 'rings' in geom_obj:
            logger.info("\n" + "=" * 50)
            logger.info("🔄 DEMO 1: RINGS → COORDINATES CONVERSION")
            logger.info("=" * 50)
            
            coords_geom = converter.convert_rings_to_coordinates(geom_obj)
            if coords_geom:
                logger.info(f"✅ Converted to {coords_geom['type']}")
                logger.info(f"📊 Coordinates structure: {len(coords_geom.get('coordinates', []))} polygons")
                
                # Lưu converted geometry
                with open('exports/rings_to_coordinates_demo.json', 'w', encoding='utf-8') as f:
                    json.dump(coords_geom, f, indent=2, ensure_ascii=False)
                logger.info("💾 Saved to exports/rings_to_coordinates_demo.json")
                
                # Demo 2: Chuyển đổi ngược từ coordinates sang rings
                logger.info("\n" + "=" * 50)
                logger.info("🔄 DEMO 2: COORDINATES → RINGS CONVERSION (Round-trip)")
                logger.info("=" * 50)
                
                rings_geom_converted = converter.convert_coordinates_to_rings(coords_geom)
                if rings_geom_converted:
                    logger.info(f"✅ Round-trip conversion successful")
                    logger.info(f"📊 Rings structure: {len(rings_geom_converted.get('rings', []))} rings")
                    
                    # Lưu converted geometry
                    with open('exports/coordinates_to_rings_demo.json', 'w', encoding='utf-8') as f:
                        json.dump(rings_geom_converted, f, indent=2, ensure_ascii=False)
                    logger.info("💾 Saved to exports/coordinates_to_rings_demo.json")
        
        # Demo 3: Test point-in-polygon với brand offices
        logger.info("\n" + "=" * 50)
        logger.info("🎯 DEMO 3: POINT-IN-POLYGON TESTING")
        logger.info("=" * 50)
        
        for brand in brand_results:
            test_point = (float(brand['latitude']), float(brand['longitude']))
            logger.info(f"\n🏢 Brand Office {brand['id']}: {test_point}")
            logger.info(f"   Address: {brand['address_old'][:60]}...")
            
            results = converter.test_point_in_polygon_both_formats(geom_obj, test_point)
            
            logger.info(f"📊 Original format: {results['original_format']}")
            logger.info(f"🎯 Rings result: {results['rings_result']} (time: {results['rings_time']:.6f}s)")
            logger.info(f"🎯 Coordinates result: {results['coordinates_result']} (time: {results['coordinates_time']:.6f}s)")
            logger.info(f"✅ Conversion success: {results['conversion_success']}")
            
            if results['rings_result'] == results['coordinates_result']:
                logger.info("✅ Results consistent!")
                if results['rings_result']:
                    logger.info(f"🎉 Brand Office {brand['id']} nằm trong {geo_result['ward_title']}!")
            else:
                logger.warning("⚠️ Results inconsistent!")
        
        # Demo 4: Performance comparison
        logger.info("\n" + "=" * 50)
        logger.info("⚡ DEMO 4: PERFORMANCE COMPARISON")
        logger.info("=" * 50)
        
        test_point = (20.95026, 107.08415)  # Known point inside geometry
        
        # Test multiple times for accurate timing
        rings_times = []
        coords_times = []
        
        for i in range(10):
            results = converter.test_point_in_polygon_both_formats(geom_obj, test_point)
            rings_times.append(results['rings_time'])
            coords_times.append(results['coordinates_time'])
        
        avg_rings_time = sum(rings_times) / len(rings_times)
        avg_coords_time = sum(coords_times) / len(coords_times)
        
        logger.info(f"📊 Average rings time: {avg_rings_time:.6f}s")
        logger.info(f"📊 Average coordinates time: {avg_coords_time:.6f}s")
        
        if avg_rings_time < avg_coords_time:
            logger.info(f"🚀 Rings format faster by {(avg_coords_time/avg_rings_time):.2f}x")
        else:
            logger.info(f"🚀 Coordinates format faster by {(avg_rings_time/avg_coords_time):.2f}x")
        
        # Final statistics
        logger.info("\n" + "=" * 50)
        logger.info("📊 FINAL STATISTICS")
        logger.info("=" * 50)
        for key, value in converter.stats.items():
            logger.info(f"  {key}: {value}")
        
        logger.info("\n💡 CONCLUSIONS:")
        logger.info("✅ Chuyển đổi hai chiều coordinates ↔ rings thành công")
        logger.info("✅ Point-in-polygon hoạt động nhất quán với cả hai format")
        logger.info("✅ Có thể tích hợp với brand_office để xác định ward")
        logger.info("💡 Rings format: Tối ưu cho ArcGIS integration")
        logger.info("💡 Coordinates format: Tối ưu cho web applications")
        
    except Exception as e:
        logger.error(f"❌ Lỗi trong demo: {e}")
    finally:
        if connection:
            connection.close()

if __name__ == "__main__":
    demo_complete_conversion()
