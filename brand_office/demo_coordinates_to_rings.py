#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo: Chuyển đổi từ Coordinates (GeoJSON) sang Rings (Esri) với Brand Office
Minh họa chuyển đổi ngược và test point-in-polygon
"""

import json
import mysql.connector
from shapely.geometry import Point, shape, Polygon, MultiPolygon
import logging
import time

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_database_connection():
    """Tạo kết nối database MySQL"""
    try:
        connection = mysql.connector.connect(
            host='127.0.0.1',
            port=3306,
            database='urbox',
            user='root',
            password='root',
            charset='utf8mb4'
        )
        
        if connection.is_connected():
            logger.info("✅ Kết nối database thành công!")
            return connection
            
    except Exception as e:
        logger.error(f"❌ Lỗi kết nối database: {e}")
        return None

def convert_coordinates_to_rings(geom_obj):
    """Chuyển đổi từ coordinates format (GeoJSON) sang rings format (Esri)"""
    try:
        if 'coordinates' in geom_obj:
            rings_geom = geom_obj.copy()
            coordinates = geom_obj['coordinates']
            
            # Xử lý theo type của geometry
            if geom_obj.get('type') == 'MultiPolygon':
                # MultiPolygon: coordinates = [[[[lon,lat], ...]]]
                rings = []
                for polygon in coordinates:
                    for ring in polygon:
                        rings.append(ring)
                rings_geom['rings'] = rings
                
            elif geom_obj.get('type') == 'Polygon':
                # Polygon: coordinates = [[[lon,lat], ...]]
                rings_geom['rings'] = coordinates
                
            # Xóa coordinates và cập nhật type
            rings_geom.pop('coordinates', None)
            rings_geom['type'] = 'esriGeometryPolygon'
            
            logger.info(f"✅ Chuyển đổi coordinates->rings: {len(rings_geom.get('rings', []))} rings")
            return rings_geom
            
        elif 'rings' in geom_obj:
            logger.info("ℹ️ Geometry đã ở định dạng rings")
            return geom_obj
            
        else:
            logger.warning("⚠️ Không tìm thấy coordinates hoặc rings trong geometry")
            return None
            
    except Exception as e:
        logger.error(f"❌ Lỗi chuyển đổi coordinates->rings: {e}")
        return None

def create_shapely_from_rings(geom_obj):
    """Tạo Shapely geometry từ rings format"""
    try:
        rings = geom_obj.get('rings', [])
        if not rings:
            return None
        
        # Flatten nested rings structure
        flat_rings = []
        for ring_group in rings:
            if isinstance(ring_group[0][0], list):
                for ring in ring_group:
                    flat_rings.append(ring)
            else:
                flat_rings.append(ring_group)
        
        # Tạo polygons từ rings
        polygons = []
        for ring in flat_rings:
            try:
                poly = Polygon(ring)
                if poly.is_valid:
                    polygons.append(poly)
                else:
                    # Try to fix invalid polygon
                    fixed_poly = poly.buffer(0)
                    if fixed_poly.is_valid:
                        polygons.append(fixed_poly)
            except Exception as e:
                logger.warning(f"⚠️ Skipping invalid ring: {e}")
                continue
        
        if len(polygons) == 0:
            return None
        elif len(polygons) == 1:
            return polygons[0]
        else:
            return MultiPolygon(polygons)
            
    except Exception as e:
        logger.error(f"❌ Lỗi tạo Shapely từ rings: {e}")
        return None

def demo_conversion_with_known_point():
    """Demo với điểm đã biết nằm trong geometry"""
    logger.info("=" * 80)
    logger.info("🚀 DEMO: COORDINATES → RINGS CONVERSION")
    logger.info("=" * 80)
    
    connection = get_database_connection()
    if not connection:
        return
    
    try:
        cursor = connection.cursor(dictionary=True)
        
        # Lấy bản ghi ID 726 (Phường Hồng Gai) - đã biết có geometry phức tạp
        query = """
        SELECT id, ward_title, province_title, KINH_DO, VI_DO, geometry
        FROM geo_ward 
        WHERE id = 726 AND geometry IS NOT NULL
        """
        cursor.execute(query)
        result = cursor.fetchone()
        cursor.close()
        
        if not result:
            logger.error("❌ Không tìm thấy bản ghi test")
            return
        
        logger.info(f"📍 Test với: {result['ward_title']}, {result['province_title']}")
        logger.info(f"📍 Geometry center: ({result['VI_DO']}, {result['KINH_DO']})")
        
        # Parse geometry
        geometry_data = json.loads(result['geometry'])
        geom_obj = geometry_data['geometry']
        
        # Test points - bao gồm center point và một số điểm khác
        test_points = [
            (float(result['VI_DO']), float(result['KINH_DO']), "Geometry Center"),
            (20.95026, 107.08415, "Custom Test Point"),
            (20.957, 107.083, "Near Center"),
            (21.0, 106.0, "Far Point (should be False)")
        ]
        
        logger.info(f"\n🔍 Original format: {geom_obj.get('type', 'Unknown')}")
        logger.info(f"🔍 Has coordinates: {'coordinates' in geom_obj}")
        logger.info(f"🔍 Has rings: {'rings' in geom_obj}")
        
        for lat, lng, description in test_points:
            logger.info(f"\n" + "=" * 50)
            logger.info(f"🎯 Testing: {description} ({lat}, {lng})")
            logger.info("=" * 50)
            
            point = Point(lng, lat)
            
            # Test với format gốc
            if 'coordinates' in geom_obj:
                logger.info("📊 COORDINATES FORMAT (Original)")
                start_time = time.time()
                coords_geometry = shape(geom_obj)
                coords_result = coords_geometry.contains(point)
                coords_time = time.time() - start_time
                logger.info(f"  🎯 Result: {coords_result}")
                logger.info(f"  ⏱️ Time: {coords_time:.6f}s")
                
                # Chuyển đổi sang rings
                logger.info("\n🔄 CONVERTING TO RINGS FORMAT")
                rings_geom = convert_coordinates_to_rings(geom_obj)
                
                if rings_geom:
                    logger.info("📊 RINGS FORMAT (Converted)")
                    start_time = time.time()
                    rings_geometry = create_shapely_from_rings(rings_geom)
                    if rings_geometry:
                        rings_result = rings_geometry.contains(point)
                        rings_time = time.time() - start_time
                        logger.info(f"  🎯 Result: {rings_result}")
                        logger.info(f"  ⏱️ Time: {rings_time:.6f}s")
                        
                        # So sánh kết quả
                        if coords_result == rings_result:
                            logger.info("  ✅ Results consistent!")
                            if coords_time < rings_time:
                                logger.info(f"  🚀 Coordinates faster by {(rings_time/coords_time):.2f}x")
                            else:
                                logger.info(f"  🚀 Rings faster by {(coords_time/rings_time):.2f}x")
                        else:
                            logger.warning("  ⚠️ Results inconsistent!")
                    else:
                        logger.error("  ❌ Failed to create Shapely from rings")
                else:
                    logger.error("  ❌ Failed to convert to rings")
            
            elif 'rings' in geom_obj:
                logger.info("📊 RINGS FORMAT (Original)")
                start_time = time.time()
                rings_geometry = create_shapely_from_rings(geom_obj)
                if rings_geometry:
                    rings_result = rings_geometry.contains(point)
                    rings_time = time.time() - start_time
                    logger.info(f"  🎯 Result: {rings_result}")
                    logger.info(f"  ⏱️ Time: {rings_time:.6f}s")
                    
                    logger.info("  ℹ️ Already in rings format - no conversion needed")
        
        # Lưu converted geometry để kiểm tra
        if 'coordinates' in geom_obj:
            rings_geom = convert_coordinates_to_rings(geom_obj)
            if rings_geom:
                with open('exports/converted_rings_demo.json', 'w', encoding='utf-8') as f:
                    json.dump(rings_geom, f, indent=2, ensure_ascii=False)
                logger.info("\n💾 Đã lưu converted rings geometry vào exports/converted_rings_demo.json")
        
        logger.info("\n" + "=" * 50)
        logger.info("💡 SUMMARY")
        logger.info("=" * 50)
        logger.info("✅ Chuyển đổi từ coordinates sang rings thành công")
        logger.info("✅ Point-in-polygon test hoạt động với cả hai format")
        logger.info("✅ Kết quả nhất quán giữa hai định dạng")
        logger.info("💡 Rings format phù hợp cho ArcGIS integration")
        logger.info("💡 Coordinates format tối ưu cho web applications")
        
    except Exception as e:
        logger.error(f"❌ Lỗi trong demo: {e}")
    finally:
        if connection:
            connection.close()

def demo_brand_office_integration():
    """Demo tích hợp với brand_office data"""
    logger.info("\n" + "=" * 80)
    logger.info("🏢 BRAND OFFICE INTEGRATION DEMO")
    logger.info("=" * 80)
    
    connection = get_database_connection()
    if not connection:
        return
    
    try:
        cursor = connection.cursor(dictionary=True)
        
        # Lấy một số brand_office có tọa độ
        brand_query = """
        SELECT id, latitude, longitude, address_old, city_id
        FROM brand_office 
        WHERE latitude IS NOT NULL AND longitude IS NOT NULL
        AND latitude BETWEEN 20.5 AND 21.5 
        AND longitude BETWEEN 106.5 AND 108.0
        AND status = 2
        LIMIT 3
        """
        cursor.execute(brand_query)
        brand_offices = cursor.fetchall()
        
        # Lấy geometry của Quảng Ninh (có thể chứa các brand_office này)
        geo_query = """
        SELECT id, ward_title, province_title, geometry
        FROM geo_ward 
        WHERE province_title LIKE '%Quảng Ninh%' 
        AND geometry IS NOT NULL
        LIMIT 2
        """
        cursor.execute(geo_query)
        geo_wards = cursor.fetchall()
        
        cursor.close()
        
        logger.info(f"📊 Testing {len(brand_offices)} brand offices với {len(geo_wards)} wards")
        
        for brand in brand_offices:
            logger.info(f"\n🏢 Brand Office {brand['id']}: ({brand['latitude']}, {brand['longitude']})")
            logger.info(f"   Address: {brand['address_old'][:50]}...")
            
            for ward in geo_wards:
                logger.info(f"\n  📍 Testing với {ward['ward_title']}")
                
                geometry_data = json.loads(ward['geometry'])
                geom_obj = geometry_data['geometry']
                
                # Test point-in-polygon với rings format
                if 'rings' in geom_obj:
                    rings_geometry = create_shapely_from_rings(geom_obj)
                    if rings_geometry:
                        point = Point(float(brand['longitude']), float(brand['latitude']))
                        is_inside = rings_geometry.contains(point)
                        logger.info(f"    🎯 Point in polygon (rings): {is_inside}")
                        
                        if is_inside:
                            logger.info(f"    🎉 Brand Office {brand['id']} thuộc {ward['ward_title']}!")
                            
                            # Demo: Cập nhật địa chỉ brand_office
                            new_address = f"{brand['address_old']}, {ward['ward_title']}, {ward['province_title']}"
                            logger.info(f"    📝 Suggested new address: {new_address[:80]}...")
        
    except Exception as e:
        logger.error(f"❌ Lỗi trong brand office demo: {e}")
    finally:
        if connection:
            connection.close()

def main():
    """Hàm main"""
    demo_conversion_with_known_point()
    demo_brand_office_integration()

if __name__ == "__main__":
    main()
