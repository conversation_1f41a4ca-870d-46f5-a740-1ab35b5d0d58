#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo: So s<PERSON>h định dạng Coordinates vs Rings trong xử lý geometry
"""

import json
import mysql.connector
from shapely.geometry import shape, Point, Polygon, MultiPolygon
import time

def get_sample_data():
    """Lấy dữ liệu mẫu từ database"""
    try:
        connection = mysql.connector.connect(
            host='127.0.0.1',
            port=3306,
            database='urbox',
            user='root',
            password='root',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor(dictionary=True)
        query = """
        SELECT id, ward_title, province_title, KINH_DO, VI_DO, geometry
        FROM geo_ward 
        WHERE id = 726 AND geometry IS NOT NULL
        """
        cursor.execute(query)
        result = cursor.fetchone()
        cursor.close()
        connection.close()
        
        return result
        
    except Exception as e:
        print(f"❌ Lỗi database: {e}")
        return None

def demo_point_in_polygon():
    """Demo kiểm tra point in polygon với cả hai định dạng"""
    print("=" * 80)
    print("🚀 DEMO: POINT IN POLYGON VỚI COORDINATES VS RINGS")
    print("=" * 80)
    
    # 1. Lấy dữ liệu mẫu
    sample_data = get_sample_data()
    if not sample_data:
        return
    
    print(f"📍 Test với: {sample_data['ward_title']}, {sample_data['province_title']}")
    print(f"📍 Geometry center: ({sample_data['VI_DO']}, {sample_data['KINH_DO']})")
    
    # Tọa độ test
    test_point = (20.95026, 107.08415)
    print(f"🎯 Test point: {test_point}")
    
    # Parse geometry
    geometry_data = json.loads(sample_data['geometry'])
    rings_geom = geometry_data['geometry']
    
    print("\n" + "=" * 50)
    print("📊 ĐỊNH DẠNG RINGS (Hiện tại trong database)")
    print("=" * 50)
    print(f"🔍 Geometry type: {rings_geom.get('type')}")
    print(f"🔍 Number of rings: {len(rings_geom.get('rings', []))}")
    
    # Tạo Shapely geometry từ rings
    rings = rings_geom['rings']
    flat_rings = []
    for ring_group in rings:
        if isinstance(ring_group[0][0], list):
            for ring in ring_group:
                flat_rings.append(ring)
        else:
            flat_rings.append(ring_group)
    
    polygons = []
    for ring in flat_rings:
        try:
            poly = Polygon(ring)
            if poly.is_valid:
                polygons.append(poly)
        except:
            continue
    
    if len(polygons) == 1:
        shapely_rings = polygons[0]
    else:
        shapely_rings = MultiPolygon(polygons)
    
    # Test point in polygon với rings
    start_time = time.time()
    point = Point(test_point[1], test_point[0])  # longitude, latitude
    rings_result = shapely_rings.contains(point)
    rings_time = time.time() - start_time
    
    print(f"🎯 Point in polygon: {rings_result}")
    print(f"⏱️ Thời gian xử lý: {rings_time:.6f}s")
    
    print("\n" + "=" * 50)
    print("🔄 CHUYỂN ĐỔI SANG COORDINATES (GeoJSON)")
    print("=" * 50)
    
    # Chuyển đổi sang coordinates format
    coordinates_geom = {
        'type': 'MultiPolygon',
        'coordinates': []
    }
    
    for ring in flat_rings:
        coordinates_geom['coordinates'].append([ring])
    
    print(f"🔍 Geometry type: {coordinates_geom['type']}")
    print(f"🔍 Number of polygons: {len(coordinates_geom['coordinates'])}")
    
    # Tạo Shapely geometry từ coordinates
    shapely_coords = shape(coordinates_geom)
    if not shapely_coords.is_valid:
        shapely_coords = shapely_coords.buffer(0)
    
    # Test point in polygon với coordinates
    start_time = time.time()
    coords_result = shapely_coords.contains(point)
    coords_time = time.time() - start_time
    
    print(f"🎯 Point in polygon: {coords_result}")
    print(f"⏱️ Thời gian xử lý: {coords_time:.6f}s")
    
    print("\n" + "=" * 50)
    print("📊 KẾT QUẢ SO SÁNH")
    print("=" * 50)
    print(f"🎯 Rings result: {rings_result}")
    print(f"🎯 Coordinates result: {coords_result}")
    print(f"⏱️ Rings time: {rings_time:.6f}s")
    print(f"⏱️ Coordinates time: {coords_time:.6f}s")
    
    if rings_result == coords_result:
        print("✅ Kết quả nhất quán giữa hai định dạng")
    else:
        print("⚠️ Kết quả khác nhau!")
    
    if rings_time < coords_time:
        print(f"🚀 Rings nhanh hơn {(coords_time/rings_time):.2f}x")
    else:
        print(f"🚀 Coordinates nhanh hơn {(rings_time/coords_time):.2f}x")
    
    print("\n" + "=" * 50)
    print("💡 GIẢI THÍCH SỰ KHÁC BIỆT")
    print("=" * 50)
    
    explanation = """
    🔍 RINGS FORMAT (Esri ArcGIS):
    - Định dạng gốc từ ArcGIS Server
    - Cấu trúc: {"rings": [[[lon,lat], ...]], "type": "MultiPolygon"}
    - Mỗi ring là một boundary hoặc hole
    - Phù hợp cho dữ liệu phức tạp với nhiều holes
    - Cần xử lý để flatten nested structure
    
    🔄 COORDINATES FORMAT (GeoJSON):
    - Chuẩn GeoJSON RFC 7946
    - Cấu trúc: {"type": "MultiPolygon", "coordinates": [[[[lon,lat], ...]]]}
    - Tương thích với hầu hết thư viện GIS
    - Dễ dàng sử dụng với Shapely, GeoPandas
    - Hiệu suất tốt cho point-in-polygon queries
    
    💡 KHUYẾN NGHỊ:
    - Sử dụng COORDINATES cho ứng dụng web, API
    - Sử dụng RINGS khi cần tương thích với ArcGIS
    - Chuyển đổi một lần và cache kết quả
    """
    
    for line in explanation.strip().split('\n'):
        print(line)

if __name__ == "__main__":
    demo_point_in_polygon()
