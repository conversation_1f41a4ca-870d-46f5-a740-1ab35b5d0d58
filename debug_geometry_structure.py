#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug Geometry Structure - Ki<PERSON>m tra cấu trúc dữ liệu geometry
"""

import json
import mysql.connector
from mysql.connector import Error
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_database_connection():
    """Tạo kết nối database MySQL"""
    try:
        connection = mysql.connector.connect(
            host='127.0.0.1',
            port=3306,
            database='urbox',
            user='root',
            password='root',
            charset='utf8mb4'
        )
        
        if connection.is_connected():
            logger.info("✅ Kết nối database thành công!")
            return connection
            
    except Error as e:
        logger.error(f"❌ Lỗi kết nối database: {e}")
        return None

def debug_geometry_structure(record_id=726):
    """Debug cấu trúc geometry"""
    connection = get_database_connection()
    if not connection:
        return
        
    try:
        cursor = connection.cursor(dictionary=True)
        query = """
        SELECT id, ward_title, province_title, KINH_DO, VI_DO, geometry
        FROM geo_ward 
        WHERE id = %s AND geometry IS NOT NULL
        """
        cursor.execute(query, (record_id,))
        result = cursor.fetchone()
        cursor.close()
        
        if not result:
            logger.error(f"Không tìm thấy bản ghi ID {record_id}")
            return
        
        logger.info(f"📍 Ward: {result['ward_title']}, {result['province_title']}")
        logger.info(f"📍 Center: ({result['VI_DO']}, {result['KINH_DO']})")
        
        # Parse geometry JSON
        geometry_str = result['geometry']
        geometry_data = json.loads(geometry_str)
        
        logger.info("🔍 Cấu trúc geometry:")
        logger.info(f"  - Top level keys: {list(geometry_data.keys())}")
        
        if 'geometry' in geometry_data:
            geom_obj = geometry_data['geometry']
            logger.info(f"  - Geometry keys: {list(geom_obj.keys())}")
            logger.info(f"  - Geometry type: {geom_obj.get('type', 'Unknown')}")
            
            if 'rings' in geom_obj:
                rings = geom_obj['rings']
                logger.info(f"  - Number of rings: {len(rings)}")
                logger.info(f"  - First ring length: {len(rings[0]) if rings else 0}")
                logger.info(f"  - First few points of first ring: {rings[0][:3] if rings and rings[0] else []}")
                
                # Lưu sample geometry để debug
                with open('sample_geometry.json', 'w', encoding='utf-8') as f:
                    json.dump(geometry_data, f, indent=2, ensure_ascii=False)
                logger.info("💾 Đã lưu sample geometry vào sample_geometry.json")
                
        elif 'coordinates' in geometry_data:
            coords = geometry_data['coordinates']
            logger.info(f"  - Coordinates structure: {type(coords)}")
            logger.info(f"  - Coordinates length: {len(coords) if isinstance(coords, list) else 'N/A'}")
        
    except Exception as e:
        logger.error(f"❌ Lỗi: {e}")
    finally:
        if connection:
            connection.close()

if __name__ == "__main__":
    debug_geometry_structure()
