#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Geometry Format Converter: Coordinates vs Rings
Chuyển đổi và so sánh định dạng geometry từ coordinates sang rings
"""

import json
import mysql.connector
from mysql.connector import Error
import geopandas as gpd
from shapely.geometry import shape, Point, Polygon, MultiPolygon
from shapely.ops import unary_union
import logging
import time
from typing import Dict, Any, Optional, Tuple

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('geometry_conversion.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class GeometryFormatConverter:
    """Class để chuyển đổi và test geometry formats"""
    
    def __init__(self):
        self.connection = None
        
    def get_database_connection(self):
        """Tạo kết nối database MySQL"""
        try:
            connection = mysql.connector.connect(
                host='127.0.0.1',
                port=3306,
                database='urbox',
                user='root',
                password='root',
                charset='utf8mb4'
            )
            
            if connection.is_connected():
                logger.info("✅ Kết nối database thành công!")
                return connection
                
        except Error as e:
            logger.error(f"❌ Lỗi kết nối database: {e}")
            return None
    
    def get_sample_geometry(self, record_id: int = 726) -> Optional[Dict[str, Any]]:
        """Lấy bản ghi geometry mẫu từ database"""
        self.connection = self.get_database_connection()
        if not self.connection:
            return None
            
        try:
            cursor = self.connection.cursor(dictionary=True)
            query = """
            SELECT id, ward_title, province_title, KINH_DO, VI_DO, geometry
            FROM geo_ward 
            WHERE id = %s AND geometry IS NOT NULL
            """
            cursor.execute(query, (record_id,))
            result = cursor.fetchone()
            cursor.close()
            
            if result:
                logger.info(f"📍 Lấy được geometry cho: {result['ward_title']}, {result['province_title']}")
                return result
            else:
                logger.warning(f"⚠️ Không tìm thấy bản ghi ID {record_id}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Lỗi truy vấn database: {e}")
            return None
        finally:
            if self.connection:
                self.connection.close()
    
    def parse_geometry_coordinates(self, geometry_str: str) -> Optional[Dict[str, Any]]:
        """Parse geometry từ JSON string với định dạng coordinates"""
        try:
            if isinstance(geometry_str, str):
                geometry_data = json.loads(geometry_str)
            else:
                geometry_data = geometry_str
            
            # Lấy geometry object từ feature
            if 'geometry' in geometry_data:
                geom_obj = geometry_data['geometry']
            else:
                geom_obj = geometry_data
            
            logger.info(f"🔍 Geometry type: {geom_obj.get('type', 'Unknown')}")
            logger.info(f"🔍 Geometry keys: {list(geom_obj.keys())}")
            
            return geom_obj
            
        except Exception as e:
            logger.error(f"❌ Lỗi parse geometry: {e}")
            return None
    
    def convert_rings_to_coordinates(self, geom_obj: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Chuyển đổi từ rings format sang coordinates format (GeoJSON)"""
        try:
            if 'rings' in geom_obj:
                coords_geom = geom_obj.copy()

                # Lấy rings data
                rings = geom_obj['rings']
                logger.info(f"🔍 Processing {len(rings)} rings")

                # Flatten nested rings structure
                flat_rings = []
                for ring_group in rings:
                    if isinstance(ring_group[0][0], list):
                        # Nested structure: [[[lon,lat], [lon,lat], ...]]
                        for ring in ring_group:
                            flat_rings.append(ring)
                    else:
                        # Flat structure: [[lon,lat], [lon,lat], ...]
                        flat_rings.append(ring_group)

                logger.info(f"🔍 Flattened to {len(flat_rings)} rings")

                # Tạo coordinates structure cho GeoJSON
                if len(flat_rings) == 1:
                    # Single polygon
                    coords_geom['coordinates'] = [flat_rings[0]]
                    coords_geom['type'] = 'Polygon'
                else:
                    # Multi-polygon - group rings into polygons
                    polygons = []
                    for ring in flat_rings:
                        polygons.append([ring])  # Each ring becomes a polygon
                    coords_geom['coordinates'] = polygons
                    coords_geom['type'] = 'MultiPolygon'

                # Xóa rings
                coords_geom.pop('rings', None)

                logger.info(f"✅ Chuyển đổi thành công sang {coords_geom['type']}")
                return coords_geom

            elif 'coordinates' in geom_obj:
                logger.info("ℹ️ Geometry đã ở định dạng coordinates")
                return geom_obj

            else:
                logger.warning("⚠️ Không tìm thấy rings hoặc coordinates trong geometry")
                return None

        except Exception as e:
            logger.error(f"❌ Lỗi chuyển đổi rings sang coordinates: {e}")
            return None
    
    def create_shapely_geometry(self, geom_obj: Dict[str, Any], format_type: str) -> Optional[Any]:
        """Tạo Shapely geometry object từ geometry data"""
        try:
            if format_type == 'coordinates':
                # Sử dụng shapely.geometry.shape cho GeoJSON format
                shapely_geom = shape(geom_obj)

            elif format_type == 'rings':
                # Tạo Polygon/MultiPolygon từ rings
                rings = geom_obj.get('rings', [])
                if not rings:
                    return None

                # Flatten nested rings structure
                flat_rings = []
                for ring_group in rings:
                    if isinstance(ring_group[0][0], list):
                        # Nested structure: [[[lon,lat], [lon,lat], ...]]
                        for ring in ring_group:
                            flat_rings.append(ring)
                    else:
                        # Flat structure: [[lon,lat], [lon,lat], ...]
                        flat_rings.append(ring_group)

                logger.info(f"🔍 Creating geometry from {len(flat_rings)} rings")

                # Tạo polygons từ rings
                polygons = []
                for ring in flat_rings:
                    try:
                        poly = Polygon(ring)
                        if poly.is_valid:
                            polygons.append(poly)
                        else:
                            # Try to fix invalid polygon
                            fixed_poly = poly.buffer(0)
                            if fixed_poly.is_valid:
                                polygons.append(fixed_poly)
                    except Exception as e:
                        logger.warning(f"⚠️ Skipping invalid ring: {e}")
                        continue

                if len(polygons) == 0:
                    logger.error("❌ No valid polygons created")
                    return None
                elif len(polygons) == 1:
                    shapely_geom = polygons[0]
                else:
                    shapely_geom = MultiPolygon(polygons)

            else:
                logger.error(f"❌ Format type không hỗ trợ: {format_type}")
                return None

            # Validate geometry
            if not shapely_geom.is_valid:
                logger.warning("⚠️ Invalid geometry detected, attempting to fix...")
                shapely_geom = shapely_geom.buffer(0)

            logger.info(f"✅ Tạo Shapely geometry thành công: {type(shapely_geom).__name__}")
            return shapely_geom

        except Exception as e:
            logger.error(f"❌ Lỗi tạo Shapely geometry: {e}")
            return None
    
    def test_point_in_polygon(self, shapely_geom: Any, test_point: Tuple[float, float]) -> bool:
        """Test xem point có nằm trong polygon không"""
        try:
            point = Point(test_point[1], test_point[0])  # longitude, latitude
            result = shapely_geom.contains(point)
            logger.info(f"🎯 Point ({test_point[0]}, {test_point[1]}) in polygon: {result}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Lỗi test point in polygon: {e}")
            return False
    
    def compare_formats(self, record_id: int = 726, test_point: Tuple[float, float] = (20.95026, 107.08415)):
        """So sánh hai định dạng geometry và test point-in-polygon"""
        logger.info("=" * 80)
        logger.info("🚀 BẮT ĐẦU SO SÁNH ĐỊNH DẠNG GEOMETRY")
        logger.info("=" * 80)
        
        # 1. Lấy dữ liệu từ database
        sample_data = self.get_sample_geometry(record_id)
        if not sample_data:
            return
        
        logger.info(f"📍 Test với: {sample_data['ward_title']}, {sample_data['province_title']}")
        logger.info(f"🎯 Test point: {test_point}")
        logger.info(f"📍 Geometry center: ({sample_data['VI_DO']}, {sample_data['KINH_DO']})")
        
        # 2. Parse geometry với định dạng rings (hiện tại)
        logger.info("\n" + "=" * 50)
        logger.info("📊 ĐỊNH DẠNG RINGS (Esri ArcGIS)")
        logger.info("=" * 50)

        rings_geom = self.parse_geometry_coordinates(sample_data['geometry'])
        if not rings_geom:
            return

        shapely_rings = self.create_shapely_geometry(rings_geom, 'rings')
        if shapely_rings:
            start_time = time.time()
            rings_result = self.test_point_in_polygon(shapely_rings, test_point)
            rings_time = time.time() - start_time
            logger.info(f"⏱️ Thời gian xử lý rings: {rings_time:.6f}s")

        # 3. Chuyển đổi sang định dạng coordinates
        logger.info("\n" + "=" * 50)
        logger.info("🔄 CHUYỂN ĐỔI SANG ĐỊNH DẠNG COORDINATES")
        logger.info("=" * 50)

        coordinates_geom = self.convert_rings_to_coordinates(rings_geom)
        if not coordinates_geom:
            return

        shapely_coords = self.create_shapely_geometry(coordinates_geom, 'coordinates')
        if shapely_coords:
            start_time = time.time()
            coords_result = self.test_point_in_polygon(shapely_coords, test_point)
            coords_time = time.time() - start_time
            logger.info(f"⏱️ Thời gian xử lý coordinates: {coords_time:.6f}s")
        
        # 4. So sánh kết quả
        logger.info("\n" + "=" * 50)
        logger.info("📊 KẾT QUẢ SO SÁNH")
        logger.info("=" * 50)
        
        if shapely_coords and shapely_rings:
            logger.info(f"🎯 Point in polygon (rings): {rings_result}")
            logger.info(f"🎯 Point in polygon (coordinates): {coords_result}")
            logger.info(f"⏱️ Thời gian rings: {rings_time:.6f}s")
            logger.info(f"⏱️ Thời gian coordinates: {coords_time:.6f}s")

            if coords_result == rings_result:
                logger.info("✅ Kết quả nhất quán giữa hai định dạng")
            else:
                logger.warning("⚠️ Kết quả khác nhau giữa hai định dạng!")

            # Hiển thị thông tin geometry
            logger.info(f"📊 Rings geometry: {type(shapely_rings).__name__}")
            logger.info(f"📊 Coordinates geometry: {type(shapely_coords).__name__}")

            # Lưu converted geometry để kiểm tra
            if coordinates_geom:
                with open('converted_coordinates.json', 'w', encoding='utf-8') as f:
                    json.dump(coordinates_geom, f, indent=2, ensure_ascii=False)
                logger.info("💾 Đã lưu converted coordinates vào converted_coordinates.json")
        
        # 5. Giải thích sự khác biệt
        self.explain_format_differences()
    
    def explain_format_differences(self):
        """Giải thích sự khác biệt giữa coordinates và rings format"""
        logger.info("\n" + "=" * 50)
        logger.info("📚 GIẢI THÍCH SỰ KHÁC BIỆT")
        logger.info("=" * 50)
        
        explanation = """
        🔍 COORDINATES FORMAT (GeoJSON Standard):
        - Tuân theo chuẩn GeoJSON RFC 7946
        - Cấu trúc: {"type": "Polygon", "coordinates": [[[lon,lat], ...]]}
        - Hỗ trợ MultiPolygon: {"type": "MultiPolygon", "coordinates": [[[[lon,lat], ...]]]}
        - Tương thích với hầu hết thư viện GIS (Shapely, GeoPandas, PostGIS)
        - Dễ dàng import/export với các định dạng khác
        
        🔄 RINGS FORMAT (Esri ArcGIS):
        - Định dạng riêng của Esri ArcGIS
        - Cấu trúc: {"rings": [[[lon,lat], ...]], "spatialReference": {...}}
        - Mỗi ring có thể là outer boundary hoặc hole
        - Thứ tự rings quan trọng: outer rings trước, holes sau
        - Tối ưu cho xử lý multi-polygon phức tạp
        
        💡 KHI NÀO SỬ DỤNG:
        - Coordinates: Khi cần tương thích chuẩn, dễ xử lý đơn giản
        - Rings: Khi xử lý geometry phức tạp với nhiều holes, tối ưu hiệu suất
        """
        
        for line in explanation.strip().split('\n'):
            logger.info(line)

def main():
    """Hàm main để chạy demo"""
    converter = GeometryFormatConverter()
    
    # Test với bản ghi ID 726 và tọa độ được cung cấp
    test_point = (20.95026, 107.08415)
    converter.compare_formats(record_id=726, test_point=test_point)

if __name__ == "__main__":
    main()
